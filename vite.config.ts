import { vitePlugin as remix } from "@remix-run/dev";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { flatRoutes } from "remix-flat-routes";

import dotenv from "dotenv";
dotenv.config({ path: ".env.development.secrets" });

declare module "@remix-run/node" {
  interface Future {
    v3_singleFetch: true;
  }
}

export default defineConfig({
  server: {
    port: 3000,
  },
  optimizeDeps: {
    include: ["@mui/icons-material"],
  },

  plugins: [
    remix({
      ignoredRouteFiles: ["**/.*", "**/*.md"],
      routes(defineRoutes) {
        return flatRoutes("routes", defineRoutes, {
          ignoredRouteFiles: ["**/.*", "**/*.md"],
          routeRegex: /^feapi\/(.+)/, // Treat feapi/* as nested routes
        });
      },
      future: {
        v3_fetcherPersist: true,
        v3_relativeSplatPath: true,
        v3_throwAbortReason: true,
        v3_lazyRouteDiscovery: true,
        v3_singleFetch: true,
        v3_routeConfig: false,
      },
    }),
    tsconfigPaths(),
  ],
});
