import { describe, expect, it, vi } from "vitest";
import { NoteTabGroup } from "./NoteTabGroup";
import { render, screen, within } from "@testing-library/react";
import {
  FollowUpStatus,
  NoteResponse,
  NoteType,
  ProcessingStatus,
} from "~/api/openapi/generated";
import { useFlag } from "~/context/flags";
import type { RevalidationState } from "@remix-run/router";
import userEvent from "@testing-library/user-event";
import { SerializeFrom } from "@remix-run/node";

describe("NoteTabGroup", () => {
  beforeEach(() => {
    vi.mock("~/context/flags");
    vi.mocked(useFlag).mockReturnValue(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const detailsTab = <div>Details Tab</div>;
  const summaryTab = <div>Summary Tab</div>;
  const transcriptTab = <div>Transcript Tab</div>;
  const prepTab = <div>Prep Tab</div>;
  const note: SerializeFrom<NoteResponse> = {
    uuid: "1",
    meetingName: "Test Meeting",
    status: ProcessingStatus.Processed,
    noteType: NoteType.MeetingRecording,
    created: new Date("2023-04-01T10:00:00.000Z"),
    modified: new Date("2023-04-01T10:00:00.000Z"),
    meetingDurationSeconds: 10,
    meetingTypeUuid: null,
    meetingCategory: "client",
    actionItems: [],
    advisorNotes: [],
    keyTakeaways: [],
    client: undefined,
    transcript: {
      utterances: [{ speaker: "speaker", start: "0", end: "1", text: "Hello" }],
    },
    summaryByTopics: {
      sections: [
        { topic: "First topic", bullets: ["first bullet", "second bullet"] },
      ],
    },
    isDeleted: false,
    botId: undefined,
    features: [],
    timesEditable: false,
  };
  const revalidator = {
    revalidate: () => {},
    state: "idle" as RevalidationState,
  };

  it("renders the right buttons and tabs when there is no content", () => {
    const noteWithoutContent = {
      ...note,
      transcript: { utterances: [] },
      summaryByTopics: { sections: [] },
    };

    render(
      <NoteTabGroup
        note={noteWithoutContent}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
        ]}
        currentTab="details"
        setCurrentTab={() => {}}
        revalidator={revalidator}
      />
    );

    expect(screen.getByRole("tab", { name: "Details" })).toBeInTheDocument();
    expect(
      screen.queryByRole("tab", { name: "Summary" })
    ).not.toBeInTheDocument();
    expect(
      screen.queryByRole("tab", { name: "Transcript" })
    ).not.toBeInTheDocument();
    // Prep and compliance tabs are present if there is a matching child.
    expect(
      screen.getByRole("tab", { name: "Meeting prep" })
    ).toBeInTheDocument();
    expect(screen.getByRole("tab", { name: "Compliance" })).toBeInTheDocument();
    expect(screen.getByText("Details Tab")).toBeInTheDocument();
    expect(screen.queryByText("Summary Tab")).not.toBeInTheDocument();
    expect(screen.queryByText("Transcript Tab")).not.toBeInTheDocument();
    expect(screen.getByText("Prep Tab")).toBeInTheDocument();
    expect(screen.getByText("Compliance Tab")).toBeInTheDocument();
  });

  it("renders the buttons and tabs when there is content", () => {
    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
        ]}
        currentTab="details"
        setCurrentTab={() => {}}
        revalidator={revalidator}
      />
    );

    expect(screen.getByRole("tab", { name: "Details" })).toBeInTheDocument();
    expect(screen.getByRole("tab", { name: "Summary" })).toBeInTheDocument();
    expect(screen.getByRole("tab", { name: "Transcript" })).toBeInTheDocument();
    expect(
      screen.getByRole("tab", { name: "Meeting prep" })
    ).toBeInTheDocument();
    expect(screen.getByRole("tab", { name: "Compliance" })).toBeInTheDocument();
    expect(screen.getByText("Details Tab")).toBeInTheDocument();
    expect(screen.getByText("Summary Tab")).toBeInTheDocument();
    expect(screen.getByText("Transcript Tab")).toBeInTheDocument();
    expect(screen.getByText("Prep Tab")).toBeInTheDocument();
    expect(screen.getByText("Compliance Tab")).toBeInTheDocument();

    expect(screen.getByText("Details Tab").parentNode).not.toHaveClass(
      "hidden"
    );
    expect(screen.getByText("Summary Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Transcript Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Compliance Tab").parentNode).toHaveClass("hidden");
  });

  it("renders the summary tab visibly", () => {
    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
        ]}
        currentTab="summary"
        setCurrentTab={() => {}}
        revalidator={revalidator}
      />
    );

    expect(screen.getByText("Details Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Summary Tab").parentNode).not.toHaveClass(
      "hidden"
    );
    expect(screen.getByText("Transcript Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Prep Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Compliance Tab").parentNode).toHaveClass("hidden");
  });

  it("renders the transcript tab visibly", () => {
    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
        ]}
        currentTab="transcript"
        setCurrentTab={() => {}}
        revalidator={revalidator}
      />
    );

    expect(screen.getByText("Details Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Summary Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Transcript Tab").parentNode).not.toHaveClass(
      "hidden"
    );
    expect(screen.getByText("Prep Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Compliance Tab").parentNode).toHaveClass("hidden");
  });

  it("renders the prep tab visibly", () => {
    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
        ]}
        currentTab="prep"
        setCurrentTab={() => {}}
        revalidator={revalidator}
      />
    );

    expect(screen.getByText("Details Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Summary Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Transcript Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Prep Tab").parentNode).not.toHaveClass("hidden");
    expect(screen.getByText("Compliance Tab").parentNode).toHaveClass("hidden");
  });

  it("renders the compliance tab visibly", () => {
    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
        ]}
        currentTab="uuid"
        setCurrentTab={() => {}}
        revalidator={revalidator}
      />
    );

    expect(screen.getByText("Details Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Summary Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Transcript Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Prep Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Compliance Tab").parentNode).not.toHaveClass(
      "hidden"
    );
    expect(screen.queryByTestId("RefreshIcon")).not.toBeInTheDocument();
    expect(screen.queryByTestId("MoreHorizIcon")).not.toBeInTheDocument();
  });

  it("does not render the compliance tab if the compliance view is disabled", () => {
    vi.mocked(useFlag).mockReturnValue(false);
    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[]}
        currentTab="details"
        setCurrentTab={() => {}}
        revalidator={revalidator}
      />
    );

    expect(
      screen.queryByRole("tab", { name: "Compliance" })
    ).not.toBeInTheDocument();
  });

  it("switches to the correct tab when a tab is clicked", async () => {
    const setCurrentTabMock = vi.fn();
    const mockSetCurrentTab = (tab: any) => setCurrentTabMock(tab);
    const user = userEvent.setup();

    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
        ]}
        currentTab="details"
        setCurrentTab={mockSetCurrentTab}
        revalidator={revalidator}
      />
    );

    await user.click(screen.getByRole("tab", { name: "Summary" }));
    expect(setCurrentTabMock).toHaveBeenCalledWith("summary");

    await user.click(screen.getByRole("tab", { name: "Transcript" }));
    expect(setCurrentTabMock).toHaveBeenCalledWith("transcript");

    await user.click(screen.getByRole("tab", { name: "Meeting prep" }));
    expect(setCurrentTabMock).toHaveBeenCalledWith("prep");

    await user.click(screen.getByRole("tab", { name: "Compliance" }));
    expect(setCurrentTabMock).toHaveBeenCalledWith("uuid");
  });

  it("switches to the correct tab when there are multiple tabs with the same name", async () => {
    let currentTab = "details";
    const setCurrentTab = (tab: string) => {
      currentTab = tab;
    };
    const user = userEvent.setup();

    const tabGroup = (currentTab: string) => {
      return (
        <NoteTabGroup
          note={note}
          detailsTab={detailsTab}
          summaryTab={summaryTab}
          prepTab={prepTab}
          transcriptTab={transcriptTab}
          followUpTabs={[
            {
              title: "Compliance",
              uuid: "uuid",
              status: FollowUpStatus.Completed,
              tab: <div>Compliance Tab</div>,
            },
            {
              title: "Compliance",
              uuid: "uuidTwo",
              status: FollowUpStatus.Completed,
              tab: <div>Compliance Tab Two</div>,
            },
          ]}
          currentTab={currentTab}
          setCurrentTab={setCurrentTab}
          revalidator={revalidator}
        />
      );
    };

    const { rerender } = render(tabGroup(currentTab));

    const [firstComplianceButton, secondComplianceButton] = screen.getAllByRole(
      "tab",
      { name: "Compliance" }
    );

    await user.click(firstComplianceButton!);
    // Rerender, to match what would happen if a parent component re-renders due to state changes.
    rerender(tabGroup(currentTab));

    expect(currentTab).toEqual("uuid");
    expect(screen.getByText("Compliance Tab").parentNode).not.toHaveClass(
      "hidden"
    );
    expect(screen.getByText("Compliance Tab Two").parentNode).toHaveClass(
      "hidden"
    );
    expect(firstComplianceButton).toHaveAttribute("aria-selected", "true");
    expect(secondComplianceButton).toHaveAttribute("aria-selected", "false");

    await user.click(secondComplianceButton!);
    rerender(tabGroup(currentTab));

    expect(currentTab).toEqual("uuidTwo");
    expect(screen.getByText("Compliance Tab").parentNode).toHaveClass("hidden");
    expect(screen.getByText("Compliance Tab Two").parentNode).not.toHaveClass(
      "hidden"
    );
    expect(firstComplianceButton).toHaveAttribute("aria-selected", "false");
    expect(secondComplianceButton).toHaveAttribute("aria-selected", "true");
  });

  it("renders a loading indicator and a refresh button when there is a processing follow-up", async () => {
    const mockedRevalidator = {
      ...revalidator,
      revalidate: vi.fn(),
    };
    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
          {
            title: "Compliance two",
            uuid: "uuidTwo",
            status: FollowUpStatus.Processing,
            tab: <div>Compliance Tab Two</div>,
          },
        ]}
        currentTab="details"
        setCurrentTab={() => {}}
        revalidator={mockedRevalidator}
      />
    );

    const secondButton = screen.getByText("Compliance two");
    expect(
      within(secondButton).getByTestId("MoreHorizIcon")
    ).toBeInTheDocument();

    const refreshButton = screen.getByTestId("RefreshIcon");
    expect(refreshButton).toBeInTheDocument();
    expect(refreshButton).toBeEnabled();

    const user = userEvent.setup();
    await user.click(refreshButton);

    expect(mockedRevalidator.revalidate).toHaveBeenCalled();
  });

  it("renders a spinner when revalidating", async () => {
    const mockedRevalidator = {
      revalidate: vi.fn(),
      state: "loading" as RevalidationState,
    };
    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
          {
            title: "Compliance two",
            uuid: "uuidTwo",
            status: FollowUpStatus.Processing,
            tab: <div>Compliance Tab Two</div>,
          },
        ]}
        currentTab="details"
        setCurrentTab={() => {}}
        revalidator={mockedRevalidator}
      />
    );

    expect(screen.queryByTestId("RefreshIcon")).not.toBeInTheDocument();
    const spinner = screen.getByTestId("spinner");
    expect(spinner).toBeInTheDocument();

    const user = userEvent.setup();
    await user.click(spinner);

    expect(mockedRevalidator.revalidate).not.toHaveBeenCalled();
  });

  it("renders an error indicator when there are failed follow-ups", async () => {
    render(
      <NoteTabGroup
        note={note}
        detailsTab={detailsTab}
        summaryTab={summaryTab}
        transcriptTab={transcriptTab}
        prepTab={prepTab}
        followUpTabs={[
          {
            title: "Compliance",
            uuid: "uuid",
            status: FollowUpStatus.Completed,
            tab: <div>Compliance Tab</div>,
          },
          {
            title: "Compliance two",
            uuid: "uuidTwo",
            status: FollowUpStatus.Failed,
            tab: <div>Compliance Tab Two</div>,
          },
        ]}
        currentTab="details"
        setCurrentTab={() => {}}
        revalidator={revalidator}
      />
    );

    expect(screen.queryByTestId("RefreshIcon")).not.toBeInTheDocument();
    expect(screen.queryByTestId("spinner")).not.toBeInTheDocument();

    const secondButton = screen.getByText("Compliance two");
    expect(within(secondButton).getByTestId("WarningIcon")).toBeInTheDocument();
  });
});
