import { describe, expect, it } from "vitest";
import { TranscriptTab } from "./TranscriptTab";
import { render, screen, within } from "@testing-library/react";
import {
  AttendeeType,
  NoteResponse,
  NoteType,
  ProcessingStatus,
} from "~/api/openapi/generated";
import { createM<PERSON>oryRouter, RouterProvider } from "react-router-dom";
import userEvent from "@testing-library/user-event";
import { SerializeFrom } from "@remix-run/node";

describe("TranscriptTab", () => {
  const note: SerializeFrom<NoteResponse> = {
    uuid: "1",
    meetingName: "Test Meeting",
    status: ProcessingStatus.Processed,
    noteType: NoteType.MeetingRecording,
    created: new Date("2023-04-01T10:00:00.000Z"),
    modified: new Date("2023-04-01T10:00:00.000Z"),
    meetingDurationSeconds: 10,
    meetingTypeUuid: null,
    meetingCategory: "client",
    actionItems: [],
    advisorNotes: [],
    keyTakeaways: [],
    client: undefined,
    attendees: [
      { uuid: "1", name: "<PERSON>", type: AttendeeType.User },
      { uuid: "2", name: "<PERSON>", type: AttendeeType.User },
    ],
    transcript: {
      utterances: [
        { speaker: "Speaker 1", text: "Hello", start: "0:01", end: "0:59" },
        { speaker: "Speaker 2", text: "Hi there", start: "1:00", end: "3:00" },
      ],
    },
    summaryByTopics: {
      sections: [
        { topic: "First topic", bullets: ["first bullet", "second bullet"] },
      ],
    },
    isDeleted: false,
    botId: undefined,
    features: ["remap_speakers"],
    timesEditable: false,
  };

  it("renders the transcript correctly", () => {
    const router = createMemoryRouter(
      [
        {
          path: "/",
          element: <TranscriptTab note={note} enableRemap={true} />,
        },
      ],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);

    const { getAllByRole } = within(screen.getByRole("list"));
    const listItems = getAllByRole("listitem");
    expect(listItems.map((item) => item.textContent)).toEqual([
      "0:01Speaker 1Hello",
      "1:00Speaker 2Hi there",
    ]);

    expect(screen.getByText("Meeting transcript")).toBeVisible();
    expect(screen.getByText("Remap Speakers")).toBeInTheDocument();
  });

  it("doesn't show the remap speakers button if remapping is not enabled", () => {
    const noteWithoutRemapping = {
      ...note,
      features: [],
    };
    const router = createMemoryRouter(
      [
        {
          path: "/",
          element: (
            <TranscriptTab note={noteWithoutRemapping} enableRemap={true} />
          ),
        },
      ],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);

    expect(screen.queryByText("Remap Speakers")).not.toBeInTheDocument();
  });

  it("opens the remap speakers modal", async () => {
    const router = createMemoryRouter(
      [
        {
          path: "/",
          element: <TranscriptTab note={note} enableRemap={true} />,
        },
      ],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);

    const user = userEvent.setup();
    await user.click(screen.getByRole("button", { name: "Remap Speakers" }));

    expect(screen.getByRole("dialog")).toBeVisible();
  });

  it("disables the remap button if remapping is disabled", async () => {
    const router = createMemoryRouter(
      [
        {
          path: "/",
          element: <TranscriptTab note={note} enableRemap={false} />,
        },
      ],
      { initialEntries: ["/"] }
    );
    render(<RouterProvider router={router} />);

    const user = userEvent.setup();
    await user.click(screen.getByRole("button", { name: "Remap Speakers" }));

    expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
  });
});
